@echo off
echo 开始兼容性打包 v2.0...
echo 专门解决SetThreadDescription兼容性问题

set QT_DIR=D:\Development\Qt\6.9.1\mingw_64
set PROJECT_DIR=D:\Code\Qt\zhiHuiRan<PERSON><PERSON>
set BUILD_DIR=%PROJECT_DIR%\build\Desktop_Qt_6_9_1_MinGW_64_bit-Release\release
set EXE_PATH=%BUILD_DIR%\SmartBurning.exe

echo 检查文件是否存在...
if not exist "%EXE_PATH%" (
    echo 错误: 找不到可执行文件 %EXE_PATH%
    echo 请先编译项目
    pause
    exit /b 1
)

echo 清理之前的部署文件...
del /q "%BUILD_DIR%\*.dll" 2>nul
rmdir /s /q "%BUILD_DIR%\platforms" 2>nul
rmdir /s /q "%BUILD_DIR%\imageformats" 2>nul
rmdir /s /q "%BUILD_DIR%\qml" 2>nul

echo 开始部署Qt依赖（兼容模式）...
"%QT_DIR%\bin\windeployqt.exe" ^
    --qmldir "%PROJECT_DIR%" ^
    --compiler-runtime ^
    --force ^
    --verbose 2 ^
    --no-translations ^
    --no-system-d3d-compiler ^
    --no-opengl-sw ^
    --no-quick-import ^
    "%EXE_PATH%"

echo 复制必要的运行时库...
copy /y "%QT_DIR%\bin\libgcc_s_seh-1.dll" "%BUILD_DIR%\" 2>nul
copy /y "%QT_DIR%\bin\libstdc++-6.dll" "%BUILD_DIR%\" 2>nul
copy /y "%QT_DIR%\bin\libwinpthread-1.dll" "%BUILD_DIR%\" 2>nul

echo 检查关键DLL是否存在...
if not exist "%BUILD_DIR%\Qt6Core.dll" (
    echo 警告: Qt6Core.dll 未找到
) else (
    echo Qt6Core.dll 已部署
)

echo 创建兼容性启动脚本...
echo @echo off > "%BUILD_DIR%\start_compatible.bat"
echo echo 启动智慧燃烧系统... >> "%BUILD_DIR%\start_compatible.bat"
echo set PATH=%%~dp0;%%PATH%% >> "%BUILD_DIR%\start_compatible.bat"
echo SmartBurning.exe >> "%BUILD_DIR%\start_compatible.bat"
echo pause >> "%BUILD_DIR%\start_compatible.bat"

echo 创建系统信息检查脚本...
echo @echo off > "%BUILD_DIR%\check_system.bat"
echo echo 检查系统兼容性... >> "%BUILD_DIR%\check_system.bat"
echo echo Windows版本: >> "%BUILD_DIR%\check_system.bat"
echo ver >> "%BUILD_DIR%\check_system.bat"
echo echo. >> "%BUILD_DIR%\check_system.bat"
echo echo 检查SetThreadDescription支持... >> "%BUILD_DIR%\check_system.bat"
echo dumpbin /exports kernel32.dll ^| findstr SetThreadDescription >> "%BUILD_DIR%\check_system.bat"
echo if errorlevel 1 ( >> "%BUILD_DIR%\check_system.bat"
echo     echo 警告: 系统不支持SetThreadDescription函数 >> "%BUILD_DIR%\check_system.bat"
echo     echo 建议升级到Windows 10 1607或更高版本 >> "%BUILD_DIR%\check_system.bat"
echo ^) else ( >> "%BUILD_DIR%\check_system.bat"
echo     echo 系统支持SetThreadDescription函数 >> "%BUILD_DIR%\check_system.bat"
echo ^) >> "%BUILD_DIR%\check_system.bat"
echo pause >> "%BUILD_DIR%\check_system.bat"

echo.
echo ========================================
echo 兼容性打包完成！
echo ========================================
echo 输出目录: %BUILD_DIR%
echo.
echo 使用说明:
echo 1. 直接运行 SmartBurning.exe
echo 2. 如果有问题，运行 start_compatible.bat
echo 3. 检查系统兼容性，运行 check_system.bat
echo.
echo 如果仍有SetThreadDescription错误，请考虑:
echo 1. 使用静态链接编译（修改.pro文件）
echo 2. 升级目标计算机的Windows版本
echo 3. 使用MSVC编译器而不是MinGW
echo.
pause
